// Enhanced Customer Management Service for PasaBuy Pal
import { prisma } from '@/lib/db'
// Define types since they don't exist in the current Prisma schema
type CustomerType = 'INDIVIDUAL' | 'BUSINESS' | 'CORPORATE'
type CustomerStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'PENDING' | 'PROSPECT'
type CustomerSegment = 'REGULAR' | 'VIP' | 'WHOLESALE' | 'RETAIL'
type LoyaltyTier = 'BRONZE' | 'SILVER' | 'GOLD' | 'PLATINUM' | 'DIAMOND'
type CommunicationType = 'EMAIL' | 'PHONE' | 'SMS' | 'MEETING' | 'NOTE'
type CommunicationDirection = 'INBOUND' | 'OUTBOUND'
type AddressType = 'BILLING' | 'SHIPPING' | 'BOTH'

export interface EnhancedCustomerData {
  // Basic information
  name: string
  customerType?: CustomerType
  status?: CustomerStatus
  
  // Contact information
  email?: string
  phone?: string
  alternatePhone?: string
  website?: string
  
  // Address information
  address?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
  
  // Business information
  businessName?: string
  taxId?: string
  businessType?: string
  
  // Customer preferences
  preferredDeliveryMethod?: string
  preferredPaymentMethod?: string
  creditLimit?: number
  paymentTerms?: number
  discountRate?: number
  
  // Customer segmentation
  segment?: CustomerSegment
  loyaltyTier?: LoyaltyTier
  
  // Relationship management
  assignedSalesRep?: string
  accountManager?: string
  referredBy?: string
  
  // Notes
  notes?: string
  internalNotes?: string
}

export interface CustomerCommunicationData {
  type: CommunicationType
  direction: CommunicationDirection
  subject?: string
  content: string
  channel?: string
  contactedBy?: string
  outcome?: string
  followUpDate?: Date
  isFollowUpRequired?: boolean
}

export interface CustomerAddressData {
  type: AddressType
  label?: string
  addressLine1: string
  addressLine2?: string
  city: string
  state?: string
  postalCode?: string
  country?: string
  isDefault?: boolean
  deliveryInstructions?: string
  accessCode?: string
  contactPerson?: string
  contactPhone?: string
}

export interface CustomerSearchFilters {
  name?: string
  customerType?: CustomerType[]
  status?: CustomerStatus[]
  segment?: CustomerSegment[]
  loyaltyTier?: LoyaltyTier[]
  assignedSalesRep?: string
  city?: string
  state?: string
  hasOrders?: boolean
  totalSpentMin?: number
  totalSpentMax?: number
  lastOrderAfter?: Date
  lastOrderBefore?: Date
  searchTerm?: string
}

export interface CustomerPerformanceMetrics {
  customerId: number
  customerName: string
  totalOrders: number
  totalSpent: number
  averageOrderValue: number
  firstOrderDate?: Date
  lastOrderDate?: Date
  daysSinceLastOrder?: number
  orderFrequency: number // Orders per month
  loyaltyScore: number // 0-100
  profitabilityScore: number // 0-100
  riskScore: number // 0-100 (higher = more risk)
}

export class EnhancedCustomerService {
  /**
   * Generate unique customer number
   */
  private static generateCustomerNumber(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 4)
    return `CUST-${timestamp}-${random}`.toUpperCase()
  }

  /**
   * Create enhanced customer with full profile
   */
  static async createCustomer(customerData: EnhancedCustomerData): Promise<any> {
    const customerNumber = this.generateCustomerNumber()

    const customer = await prisma.customer.create({
      data: {
        name: customerData.name.trim(),
        customerNumber,
        customerType: customerData.customerType || 'INDIVIDUAL',
        status: customerData.status || 'ACTIVE',
        
        // Contact information
        email: customerData.email?.trim().toLowerCase() || null,
        phone: customerData.phone?.trim() || null,
        alternatePhone: customerData.alternatePhone?.trim() || null,
        website: customerData.website?.trim() || null,
        
        // Address information
        address: customerData.address?.trim() || null,
        city: customerData.city?.trim() || null,
        state: customerData.state?.trim() || null,
        postalCode: customerData.postalCode?.trim() || null,
        country: customerData.country?.trim() || 'Philippines',
        
        // Business information
        businessName: customerData.businessName?.trim() || null,
        taxId: customerData.taxId?.trim() || null,
        businessType: customerData.businessType?.trim() || null,
        
        // Customer preferences
        preferredDeliveryMethod: customerData.preferredDeliveryMethod?.trim() || null,
        preferredPaymentMethod: customerData.preferredPaymentMethod?.trim() || null,
        creditLimit: customerData.creditLimit || 0,
        paymentTerms: customerData.paymentTerms || 30,
        discountRate: customerData.discountRate || 0,
        
        // Customer segmentation
        segment: customerData.segment || 'REGULAR',
        loyaltyTier: customerData.loyaltyTier || 'BRONZE',
        
        // Relationship management
        assignedSalesRep: customerData.assignedSalesRep?.trim() || null,
        accountManager: customerData.accountManager?.trim() || null,
        referredBy: customerData.referredBy?.trim() || null,
        
        // Notes
        notes: customerData.notes?.trim() || null,
        internalNotes: customerData.internalNotes?.trim() || null,
      }
    })

    return customer
  }

  /**
   * Update customer with metrics recalculation
   */
  static async updateCustomer(customerId: number, updateData: Partial<EnhancedCustomerData>): Promise<any> {
    const customer = await prisma.customer.update({
      where: { id: customerId },
      data: {
        ...updateData,
        updatedAt: new Date()
      }
    })

    // Recalculate customer metrics
    await this.recalculateCustomerMetrics(customerId)

    return customer
  }

  /**
   * Add communication record to customer
   */
  static async addCommunication(customerId: number, communicationData: CustomerCommunicationData): Promise<void> {
    await prisma.customerCommunication.create({
      data: {
        customerId,
        ...communicationData
      }
    })

    // Update last contact date
    await prisma.customer.update({
      where: { id: customerId },
      data: { lastContactDate: new Date() }
    })
  }

  /**
   * Add address to customer
   */
  static async addCustomerAddress(customerId: number, addressData: CustomerAddressData): Promise<any> {
    // If this is set as default, unset other default addresses of the same type
    if (addressData.isDefault) {
      await prisma.customerAddress.updateMany({
        where: {
          customerId,
          type: addressData.type
        },
        data: { isDefault: false }
      })
    }

    return await prisma.customerAddress.create({
      data: {
        customerId,
        ...addressData
      }
    })
  }

  /**
   * Advanced customer search with enhanced filters
   */
  static async searchCustomers(
    filters: CustomerSearchFilters,
    pagination?: { page: number; limit: number },
    sorting?: { field: string; direction: 'asc' | 'desc' }
  ): Promise<{ customers: any[]; total: number; hasMore: boolean }> {
    const whereClause: any = {}

    // Basic filters
    if (filters.name) {
      whereClause.name = { contains: filters.name, mode: 'insensitive' }
    }
    if (filters.customerType && filters.customerType.length > 0) {
      whereClause.customerType = { in: filters.customerType }
    }
    if (filters.status && filters.status.length > 0) {
      whereClause.status = { in: filters.status }
    }
    if (filters.segment && filters.segment.length > 0) {
      whereClause.segment = { in: filters.segment }
    }
    if (filters.loyaltyTier && filters.loyaltyTier.length > 0) {
      whereClause.loyaltyTier = { in: filters.loyaltyTier }
    }
    if (filters.assignedSalesRep) {
      whereClause.assignedSalesRep = filters.assignedSalesRep
    }
    if (filters.city) {
      whereClause.city = { contains: filters.city, mode: 'insensitive' }
    }
    if (filters.state) {
      whereClause.state = { contains: filters.state, mode: 'insensitive' }
    }

    // Order-related filters
    if (filters.hasOrders !== undefined) {
      if (filters.hasOrders) {
        whereClause.orders = { some: {} }
      } else {
        whereClause.orders = { none: {} }
      }
    }

    // Spending filters
    if (filters.totalSpentMin || filters.totalSpentMax) {
      whereClause.totalSpent = {}
      if (filters.totalSpentMin) whereClause.totalSpent.gte = filters.totalSpentMin
      if (filters.totalSpentMax) whereClause.totalSpent.lte = filters.totalSpentMax
    }

    // Date filters
    if (filters.lastOrderAfter || filters.lastOrderBefore) {
      whereClause.lastOrderDate = {}
      if (filters.lastOrderAfter) whereClause.lastOrderDate.gte = filters.lastOrderAfter
      if (filters.lastOrderBefore) whereClause.lastOrderDate.lte = filters.lastOrderBefore
    }

    // Search term
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.trim()
      whereClause.OR = [
        { name: { contains: searchTerm, mode: 'insensitive' } },
        { customerNumber: { contains: searchTerm, mode: 'insensitive' } },
        { email: { contains: searchTerm, mode: 'insensitive' } },
        { phone: { contains: searchTerm, mode: 'insensitive' } },
        { businessName: { contains: searchTerm, mode: 'insensitive' } }
      ]
    }

    // Pagination
    const page = pagination?.page || 1
    const limit = pagination?.limit || 50
    const skip = (page - 1) * limit

    // Sorting
    const orderBy: any = {}
    if (sorting) {
      orderBy[sorting.field] = sorting.direction
    } else {
      orderBy.name = 'asc'
    }

    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where: whereClause,
        include: {
          orders: true, // Get all orders for accurate calculation
          addresses: true,
          communications: {
            take: 3,
            orderBy: { createdAt: 'desc' }
          },
          _count: {
            select: {
              orders: true,
            }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.customer.count({ where: whereClause })
    ])

    // Calculate real-time metrics for each customer
    const customersWithMetrics = customers.map(customer => {
      const orders = customer.orders
      const totalOrders = orders.length
      const totalSpent = orders.reduce((sum, order) => sum + (order.quantity * order.customerPrice), 0)
      const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

      // Calculate additional counts
      const toBuyCount = orders.filter(order => !order.isBought).length
      const toPackCount = orders.filter(order => order.isBought && order.packingStatus === 'Not Packed').length

      // Calculate appropriate loyalty tier
      const calculatedLoyaltyTier = this.calculateLoyaltyTier(totalSpent, totalOrders)

      return {
        ...customer,
        totalSpent,
        averageOrderValue,
        loyaltyTier: calculatedLoyaltyTier, // Use calculated tier instead of database value
        _count: {
          ...customer._count,
          toBuy: toBuyCount,
          toPack: toPackCount
        },
        // Limit orders to recent 5 for response size
        orders: orders.slice(0, 5).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      }
    })

    return {
      customers: customersWithMetrics,
      total,
      hasMore: skip + customers.length < total
    }
  }

  /**
   * Calculate loyalty tier based on spending thresholds
   */
  static calculateLoyaltyTier(totalSpent: number, totalOrders: number): LoyaltyTier {
    // Define spending thresholds for each tier
    if (totalSpent >= 500000 && totalOrders >= 20) return 'DIAMOND'  // ₱500,000+ and 20+ orders
    if (totalSpent >= 200000 && totalOrders >= 10) return 'PLATINUM' // ₱200,000+ and 10+ orders
    if (totalSpent >= 100000 && totalOrders >= 5) return 'GOLD'      // ₱100,000+ and 5+ orders
    if (totalSpent >= 50000 && totalOrders >= 3) return 'SILVER'     // ₱50,000+ and 3+ orders
    return 'BRONZE'                                                   // Default tier
  }

  /**
   * Recalculate customer metrics and update loyalty tier
   */
  static async recalculateCustomerMetrics(customerId: number): Promise<void> {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        orders: {
          orderBy: { createdAt: 'asc' }
        }
      }
    })

    if (!customer) return

    const orders = customer.orders
    const totalOrders = orders.length
    const totalSpent = orders.reduce((sum, order) => sum + (order.quantity * order.customerPrice), 0)
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

    const firstOrderDate = orders.length > 0 ? orders[0].createdAt : null
    const lastOrderDate = orders.length > 0 ? orders[orders.length - 1].createdAt : null

    // Calculate appropriate loyalty tier
    const newLoyaltyTier = this.calculateLoyaltyTier(totalSpent, totalOrders)

    await prisma.customer.update({
      where: { id: customerId },
      data: {
        totalOrders,
        totalSpent,
        averageOrderValue,
        firstOrderDate,
        lastOrderDate,
        loyaltyTier: newLoyaltyTier
      }
    })
  }

  /**
   * Get customer performance metrics
   */
  static async getCustomerPerformanceMetrics(customerId: number): Promise<CustomerPerformanceMetrics> {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        orders: {
          orderBy: { createdAt: 'asc' }
        }
      }
    })

    if (!customer) {
      throw new Error('Customer not found')
    }

    const orders = customer.orders
    const totalOrders = orders.length
    const totalSpent = orders.reduce((sum, order) => sum + (order.quantity * order.customerPrice), 0)
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0

    const firstOrderDate = orders.length > 0 ? orders[0].createdAt : undefined
    const lastOrderDate = orders.length > 0 ? orders[orders.length - 1].createdAt : undefined

    // Calculate days since last order
    const daysSinceLastOrder = lastOrderDate 
      ? Math.floor((Date.now() - lastOrderDate.getTime()) / (1000 * 60 * 60 * 24))
      : undefined

    // Calculate order frequency (orders per month)
    const orderFrequency = firstOrderDate && lastOrderDate
      ? totalOrders / Math.max(1, (lastOrderDate.getTime() - firstOrderDate.getTime()) / (1000 * 60 * 60 * 24 * 30))
      : 0

    // Calculate loyalty score (0-100)
    const loyaltyScore = this.calculateLoyaltyScore({
      totalOrders,
      totalSpent,
      daysSinceLastOrder,
      orderFrequency,
      loyaltyTier: customer.loyaltyTier as LoyaltyTier
    })

    // Calculate profitability score (0-100)
    const profitabilityScore = this.calculateProfitabilityScore({
      totalSpent,
      averageOrderValue,
      orderFrequency
    })

    // Calculate risk score (0-100, higher = more risk)
    const riskScore = this.calculateRiskScore({
      daysSinceLastOrder,
      orderFrequency,
      totalOrders,
      status: customer.status as CustomerStatus
    })

    return {
      customerId: customer.id,
      customerName: customer.name,
      totalOrders,
      totalSpent,
      averageOrderValue,
      firstOrderDate,
      lastOrderDate,
      daysSinceLastOrder,
      orderFrequency,
      loyaltyScore,
      profitabilityScore,
      riskScore
    }
  }

  /**
   * Calculate loyalty score
   */
  private static calculateLoyaltyScore(data: {
    totalOrders: number
    totalSpent: number
    daysSinceLastOrder?: number
    orderFrequency: number
    loyaltyTier: LoyaltyTier
  }): number {
    let score = 0

    // Order count contribution (0-30 points)
    score += Math.min(30, data.totalOrders * 2)

    // Spending contribution (0-25 points)
    score += Math.min(25, data.totalSpent / 1000)

    // Recency contribution (0-20 points)
    if (data.daysSinceLastOrder !== undefined) {
      score += Math.max(0, 20 - (data.daysSinceLastOrder / 30))
    }

    // Frequency contribution (0-15 points)
    score += Math.min(15, data.orderFrequency * 5)

    // Loyalty tier bonus (0-10 points)
    const tierBonus = {
      BRONZE: 0,
      SILVER: 2,
      GOLD: 5,
      PLATINUM: 8,
      DIAMOND: 10
    }
    score += tierBonus[data.loyaltyTier] || 0

    return Math.min(100, Math.max(0, score))
  }

  /**
   * Calculate profitability score
   */
  private static calculateProfitabilityScore(data: {
    totalSpent: number
    averageOrderValue: number
    orderFrequency: number
  }): number {
    let score = 0

    // Total spending contribution (0-40 points)
    score += Math.min(40, data.totalSpent / 2000)

    // Average order value contribution (0-30 points)
    score += Math.min(30, data.averageOrderValue / 100)

    // Order frequency contribution (0-30 points)
    score += Math.min(30, data.orderFrequency * 10)

    return Math.min(100, Math.max(0, score))
  }

  /**
   * Calculate risk score
   */
  private static calculateRiskScore(data: {
    daysSinceLastOrder?: number
    orderFrequency: number
    totalOrders: number
    status: CustomerStatus
  }): number {
    let score = 0

    // Recency risk (0-40 points)
    if (data.daysSinceLastOrder !== undefined) {
      if (data.daysSinceLastOrder > 180) score += 40
      else if (data.daysSinceLastOrder > 90) score += 25
      else if (data.daysSinceLastOrder > 30) score += 10
    }

    // Frequency risk (0-30 points)
    if (data.orderFrequency < 0.1) score += 30
    else if (data.orderFrequency < 0.5) score += 15
    else if (data.orderFrequency < 1) score += 5

    // Order count risk (0-20 points)
    if (data.totalOrders === 1) score += 20
    else if (data.totalOrders < 5) score += 10

    // Status risk (0-10 points)
    if (data.status === 'INACTIVE') score += 10
    else if (data.status === 'SUSPENDED') score += 8
    else if (data.status === 'PROSPECT') score += 5

    return Math.min(100, Math.max(0, score))
  }

  /**
   * Get customer segmentation analytics
   */
  static async getCustomerSegmentationAnalytics(): Promise<{
    byType: Array<{ type: string; count: number; totalValue: number }>
    bySegment: Array<{ segment: string; count: number; totalValue: number }>
    byLoyaltyTier: Array<{ tier: string; count: number; totalValue: number }>
    byStatus: Array<{ status: string; count: number }>
  }> {
    const [byType, bySegment, byLoyaltyTier, byStatus] = await Promise.all([
      prisma.customer.groupBy({
        by: ['customerType'],
        _count: true,
        _sum: { totalSpent: true }
      }),
      prisma.customer.groupBy({
        by: ['segment'],
        _count: true,
        _sum: { totalSpent: true }
      }),
      prisma.customer.groupBy({
        by: ['loyaltyTier'],
        _count: true,
        _sum: { totalSpent: true }
      }),
      prisma.customer.groupBy({
        by: ['status'],
        _count: true
      })
    ])

    return {
      byType: byType.map(item => ({
        type: item.customerType,
        count: item._count,
        totalValue: item._sum.totalSpent || 0
      })),
      bySegment: bySegment.map(item => ({
        segment: item.segment,
        count: item._count,
        totalValue: item._sum.totalSpent || 0
      })),
      byLoyaltyTier: byLoyaltyTier.map(item => ({
        tier: item.loyaltyTier,
        count: item._count,
        totalValue: item._sum.totalSpent || 0
      })),
      byStatus: byStatus.map(item => ({
        status: item.status,
        count: item._count
      }))
    }
  }
}
