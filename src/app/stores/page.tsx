'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { SimplePageWrapper } from '@/components/layout/page-wrapper'
import { StoreImportDialog } from '@/components/stores/store-import-dialog'
import { StoreExportDialog } from '@/components/stores/store-export-dialog'
import { StoreBulkActionsBar, StoreBulkConfirmationDialog } from '@/components/stores/store-bulk-actions'
import { useBulkOperations, calculateSelectAllState } from '@/hooks/use-bulk-operations'
import Link from 'next/link'
import {
  LuStore,
  LuUpload,
  LuDownload,
  LuSearch,
  LuFilter,
  LuMapPin,
  LuClock,
  LuTruck
} from 'react-icons/lu'
import { useAppStore } from '@/lib/store'



interface StoreWithMetrics {
  id: number
  code: string
  name: string | null
  storeType?: string
  status?: string
  city?: string
  isOpen?: boolean
  allowsDelivery?: boolean
  allowsPickup?: boolean
  _count?: {
    orders: number
  }
  totalOrders?: number
  totalRevenue?: number
  averageOrderValue?: number
}

export default function StoresPage() {
  const router = useRouter()
  const { storeCodes, setStoreCodes, isLoadingStoreCodes, setLoadingStoreCodes } = useAppStore()
  const [error, setError] = useState<string | null>(null)

  // Enhanced functionality state
  const [stores, setStores] = useState<StoreWithMetrics[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [showExportDialog, setShowExportDialog] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  // Bulk operations
  const bulkOps = useBulkOperations()
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean
    action: 'update' | 'delete'
    isLoading: boolean
  }>({
    isOpen: false,
    action: 'update',
    isLoading: false
  })

  // Filters
  const [filters, setFilters] = useState({
    storeType: '',
    status: '',
    isOpen: undefined as boolean | undefined,
    allowsDelivery: undefined as boolean | undefined
  })

  useEffect(() => {
    async function fetchStoreCodes() {
      try {
        setLoadingStoreCodes(true)
        setError(null)

        const response = await fetch('/api/appwrite/store-codes')
        if (!response.ok) {
          throw new Error('Failed to fetch store codes')
        }

        const data = await response.json()
        setStoreCodes(data)
        setStores(data) // Also set enhanced stores data
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoadingStoreCodes(false)
      }
    }

    fetchStoreCodes()
  }, [setStoreCodes, setLoadingStoreCodes])

  // Filter stores based on search and filters
  const filteredStores = stores.filter(store => {
    const matchesSearch = !searchTerm ||
      store.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.name?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStoreType = !filters.storeType || store.storeType === filters.storeType
    const matchesStatus = !filters.status || store.status === filters.status
    const matchesIsOpen = filters.isOpen === undefined || store.isOpen === filters.isOpen
    const matchesDelivery = filters.allowsDelivery === undefined || store.allowsDelivery === filters.allowsDelivery

    return matchesSearch && matchesStoreType && matchesStatus && matchesIsOpen && matchesDelivery
  })

  // Bulk operation handlers
  const handleBulkUpdate = () => {
    setConfirmDialog({
      isOpen: true,
      action: 'update',
      isLoading: false
    })
  }

  const handleBulkDelete = () => {
    setConfirmDialog({
      isOpen: true,
      action: 'delete',
      isLoading: false
    })
  }

  const handleBulkConfirm = async () => {
    setConfirmDialog(prev => ({ ...prev, isLoading: true }))

    try {
      const selectedIds = Array.from(bulkOps.selectedItems)
      const endpoint = '/api/enhanced/stores/bulk'

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation: confirmDialog.action === 'delete' ? 'DELETE' : 'UPDATE',
          data: confirmDialog.action === 'delete'
            ? { storeIds: selectedIds }
            : { updates: selectedIds.map(id => ({ storeId: id, data: {} })) }
        })
      })

      if (response.ok) {
        // Refresh the stores list
        window.location.reload()
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Bulk operation failed')
      }
    } catch (error) {
      console.error('Bulk operation error:', error)
      setError('Failed to perform bulk operation')
    } finally {
      setConfirmDialog({ isOpen: false, action: 'update', isLoading: false })
      bulkOps.clearSelection()
    }
  }

  const handleBulkCancel = () => {
    setConfirmDialog({ isOpen: false, action: 'update', isLoading: false })
  }

  const handleStoreLongPress = (storeId: number) => {
    bulkOps.selectItemAndEnterBulkMode(storeId)
  }

  const handleCardClick = (storeId: number, event: React.MouseEvent) => {
    // Prevent navigation if in bulk mode or clicking on interactive elements
    if (bulkOps.isBulkMode) return

    const target = event.target as HTMLElement
    const isInteractiveElement = target.closest(
      'button, a, [role="button"], [data-radix-collection-item], [data-state], input, select, textarea'
    )

    if (!isInteractiveElement) {
      router.push(`/stores/${storeId}`)
    }
  }

  if (error) {
    return (
      <SimplePageWrapper title="Store Management">
        <Card className="p-6">
          <div className="text-center">
            <h2 className="text-sm font-medium text-destructive">Error</h2>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              Try Again
            </Button>
          </div>
        </Card>
      </SimplePageWrapper>
    )
  }

  return (
    <SimplePageWrapper title="Store Management">
      {/* Action Bar */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search stores..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2"
          >
            <LuFilter className="h-4 w-4" />
            Filters
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowImportDialog(true)}
            className="flex items-center gap-2"
          >
            <LuUpload className="h-4 w-4" />
            Import
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowExportDialog(true)}
            className="flex items-center gap-2"
          >
            <LuDownload className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <LuStore className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">{stores.length}</div>
              <div className="text-sm text-muted-foreground">Total Stores</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <LuClock className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {stores.filter(s => s.isOpen).length}
              </div>
              <div className="text-sm text-muted-foreground">Currently Open</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <LuTruck className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {stores.filter(s => s.allowsDelivery).length}
              </div>
              <div className="text-sm text-muted-foreground">Delivery Available</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <LuMapPin className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">
                {new Set(stores.map(s => s.city).filter(Boolean)).size}
              </div>
              <div className="text-sm text-muted-foreground">Cities</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Bulk Actions Bar */}
      {(bulkOps.isBulkMode || bulkOps.hasSelection) && (
        <StoreBulkActionsBar
          selectedCount={bulkOps.selectedCount}
          totalCount={filteredStores.length}
          isSelectAllChecked={calculateSelectAllState(bulkOps.selectedItems, filteredStores.map(s => s.id)).isSelectAllChecked}
          isSelectAllIndeterminate={calculateSelectAllState(bulkOps.selectedItems, filteredStores.map(s => s.id)).isSelectAllIndeterminate}
          isBulkMode={bulkOps.isBulkMode}
          onSelectAllChange={() => bulkOps.toggleSelectAll(filteredStores.map(s => s.id))}
          onClearSelection={bulkOps.clearSelection}
          onExitBulkMode={bulkOps.exitBulkMode}
          onBulkUpdate={handleBulkUpdate}
          onBulkDelete={handleBulkDelete}
          className="mb-4"
        />
      )}

      {/* Stores Grid */}
      {isLoadingStoreCodes ? (
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-muted/50 rounded"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-muted/50 rounded w-3/4"></div>
                    <div className="h-3 bg-muted/50 rounded w-1/2 mt-2"></div>
                  </div>
                </div>
                <div className="mt-4 h-8 bg-muted/50 rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      ) : filteredStores.length === 0 ? (
        <Card className="p-6">
          <div className="text-center">
            <LuStore className="mx-auto h-12 w-12 text-muted-foreground" />
            <h2 className="text-sm font-medium mt-2">No stores found</h2>
            <p className="text-sm text-muted-foreground mt-1">
              {searchTerm || Object.values(filters).some(Boolean)
                ? 'Try adjusting your search or filters.'
                : 'Use the + button below to add your first store.'}
            </p>
          </div>
        </Card>
      ) : (
        <div className="grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
          {filteredStores.map((store) => (
            <Card
              key={store.id}
              className="p-2.5 cursor-pointer transition-all duration-200 hover:shadow-md hover:bg-accent/30 dark:hover:bg-accent/20 group"
              onClick={(e) => handleCardClick(store.id, e)}
              onContextMenu={(e) => {
                e.preventDefault()
                handleStoreLongPress(store.id)
              }}
              title={`Click to view details for ${store.name || store.code}`}
            >
              <div className="flex items-center justify-between">
                {/* Selection checkbox - only show in bulk mode */}
                {bulkOps.isBulkMode && (
                  <div className="mr-3">
                    <Checkbox
                      checked={bulkOps.isItemSelected(store.id)}
                      onCheckedChange={() => bulkOps.toggleItem(store.id)}
                    />
                  </div>
                )}

                <div className="flex items-center space-x-2 flex-1 min-w-0">
                  <div className="w-7 h-7 bg-primary rounded flex items-center justify-center text-primary-foreground font-bold text-xs flex-shrink-0">
                    {store.code.slice(0, 2)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-sm truncate">
                      {store.name || store.code}
                    </h3>
                    <div className="flex items-center gap-1.5 mt-0.5">
                      <p className="text-xs text-muted-foreground">
                        {store.code}
                      </p>
                      <Badge variant="secondary" className="text-xs px-1.5 py-0.5 h-4">
                        {store._count?.orders || 0}
                      </Badge>
                      {store.storeType && (
                        <Badge variant="outline" className="text-xs px-1.5 py-0.5 h-4">
                          {store.storeType}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {!bulkOps.isBulkMode && (
                  <div className="flex gap-1 flex-shrink-0 ml-2">
                    <Button asChild variant="outline" size="sm" className="h-6 px-2 text-xs">
                      <Link href={`/stores/${store.id}/edit`}>
                        Edit
                      </Link>
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Dialogs */}
      <StoreImportDialog
        isOpen={showImportDialog}
        onClose={() => setShowImportDialog(false)}
        onImportComplete={() => {
          setShowImportDialog(false)
          window.location.reload()
        }}
      />

      <StoreExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        onExportComplete={() => setShowExportDialog(false)}
      />

      <StoreBulkConfirmationDialog
        isOpen={confirmDialog.isOpen}
        onClose={handleBulkCancel}
        onConfirm={handleBulkConfirm}
        action={confirmDialog.action}
        selectedCount={bulkOps.selectedCount}
        isLoading={confirmDialog.isLoading}
      />
    </SimplePageWrapper>
  )
}
