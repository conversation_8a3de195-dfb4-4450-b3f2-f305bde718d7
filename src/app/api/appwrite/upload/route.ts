import { NextRequest, NextResponse } from 'next/server'
import { appwriteStorageService } from '@/lib/services/appwrite-storage-service'
import { authService } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('image') as File
    const folder = formData.get('folder') as string || 'orders'

    if (!file) {
      return NextResponse.json(
        { error: 'No file uploaded' },
        { status: 400 }
      )
    }

    // Upload file to Appwrite Storage
    const uploadResult = await appwriteStorageService.uploadFile(file, {
      folder,
      maxSize: 10, // 10MB limit
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
    })

    return NextResponse.json({
      success: true,
      fileId: uploadResult.fileId,
      filename: uploadResult.filename,
      url: uploadResult.url,
      size: uploadResult.size,
      mimeType: uploadResult.mimeType
    })
  } catch (error: any) {
    console.error('Error uploading file to Appwrite:', error)
    return NextResponse.json(
      { error: error.message || 'Error uploading file' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      )
    }

    await appwriteStorageService.deleteFile(fileId)

    return NextResponse.json({
      success: true,
      message: 'File deleted successfully'
    })
  } catch (error: any) {
    console.error('Error deleting file from Appwrite:', error)
    return NextResponse.json(
      { error: error.message || 'Error deleting file' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const userId = await authService.getUserId()
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID is required' },
        { status: 400 }
      )
    }

    const metadata = await appwriteStorageService.getFileMetadata(fileId)

    return NextResponse.json(metadata)
  } catch (error: any) {
    console.error('Error getting file metadata from Appwrite:', error)
    return NextResponse.json(
      { error: error.message || 'Error getting file metadata' },
      { status: 500 }
    )
  }
}
